using Stratkit.Properties.Loader;
using UnityEngine;

namespace Stratkit.Data.Provinces {
    /// <summary>
    /// Scriptable property for tagging provinces as Points of Interest.
    /// Adds ProvincePoiTag to provinces that have this entry in balancing data.
    /// </summary>
    [CreateAssetMenu(
        fileName = nameof(ProvincePoiTagScriptable),
        menuName = "Stratkit/Balancing Data/Provinces/" + nameof(ProvincePoiTagScriptable),
        order = 0
    )]
    public sealed partial class ProvincePoiTagScriptable : PropertyScriptableUnmanaged<ProvincePoiTag> { }
}

