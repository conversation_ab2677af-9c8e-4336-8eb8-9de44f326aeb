using Stratkit.Data.Provinces;
using Stratkit.MapMaker.ServerExport.Data;
using Stratkit.Properties.Loader;

namespace Stratkit.MapMaker.Editor {
    /// <summary>
    /// Imports Point-Of-Interest (POI) information for provinces.
    /// Reads "pointOfInterest" from the MJS location and bakes ProvincePoiTag via balancing.
    /// </summary>
    public sealed class POIMjsBalancingImporter : ILocationMjsBalancingImporter {
        /// <summary>
        /// Import POI data for a province.
        /// </summary>
        /// <param name="propertyScriptable">Provinces property scriptable.</param>
        /// <param name="location">Reference to location.</param>
        /// <param name="locationId">Id of location.</param>
        public void ImportBalancingData(BasePropertyScriptable propertyScriptable, MjsLocation location, int locationId) {
            if (propertyScriptable is not ProvincePoiTagScriptable poiTagScriptable) {
                return;
            }

            // pointOfInterest is optional in the schema; treat null/false as not POI
            bool isPoi = location.PointOfInterest is true;

            if (!isPoi) {
                // Safety: ensure we don't create the tag if not a POI
                if (poiTagScriptable.HasElement(locationId)) {
                    poiTagScriptable.RemoveElement(locationId);
                }
                return;
            }

            // When POI is true, ensure the tag entry exists (AddNewDefault is already called upstream,
            // but we guard in case this importer is used in isolation)
            if (!poiTagScriptable.HasElement(locationId)) {
                poiTagScriptable.AddNewDefault(locationId);
            }
        }
    }
}

