# com.stratkit.contentitemsmodel

_When adding new versions, add it at the end of the file for simplicity_
_Tag with [P10-XXXX] when the task has a ticket related. E.g. - [P10-1234](https://bytrolabs.atlassian.net/browse/P10-1234) Fix NRE on startup_

## Version 0.1.0
 - Initial version

## Version 0.1.1
 - Fixes issues with disabled domain reloading.

## Version 0.1.2
 - [WHS-3685](https://bytrolabs.atlassian.net/browse/WHS-3685) Fixes `ContentItemsDb` throwing exceptions when a new world is created that shares the name with a previous one (e.g. when switching from one match to another, with the world map).

## Version 1.0.0
 - Remove caching of entity queries.

## Version 1.1.0
 - Adds UnitContentItem json parsing.

## Version 1.2.0
 - Adds parsing of multi-version content items.

## Version 1.3.0
 - Add support for IContentItemsTable

## Version 1.4.0
 - Add support for loading backend content item as JSONs.

## Version 1.5.0
 - Combine Front-End and Back-End content item in a single interface

## Version 1.6.0
 - Adjust ContentItem casing and converters to support C# naming conventions.

## Version 2.0.0
 - Remove OldDatabase usage.
 - Adjust Enums to support deserialization from strings.

## Version 2.1.0
 - Additional utilities for importing data into `ContentItemsBackendScriptable`.

## Version 2.2.0
 - Add Id Space for ContentItemId

## Version 2.2.1
 - Adjustments to addressables asset loading.

## Version 2.3.0
 - Add GetContent using Entity for IContentItemsDbManaged

## Version 2.3.1
 - Fixes balance importing issues.

## Version 3.0.0
 - Adds generated Content Item classes.

## Version 3.0.1
 - Fixes missing DOTS counterpart initialization of UnitContent.

## Version 3.0.2
 - Fixes Content Items initialization.

## Version 3.1.0
 - Adds `ModFeature.RestrictedRailroadUsage`.

## Version 3.1.1
 - Adds missing steps to fix the CSV to JSON conversion.

## Version 3.2.0
 - Add constructor to auto convert ContentItemId to ResourceId

## Version 3.3.0
 - Apply auto-refactor for BE-Content-Items

## Version 3.4.0
 - Add some extension methods to ScenarioContent

## Version 3.5.0
 - Compatibility fixes for ContentItemsModel

## Version 3.5.1
 - Fix ContentItemsTablesCache.HasValue and related functions.

## Version 3.6.0
 - Replace GameGoalContent components

## Version 3.6.1
 - Fix GameGoalContents not sealed

## Version 3.7.0
 - Replace OfferContent components

## Version 3.8.0
 - Add post import hooks for `ContentItemsBackendScriptable`

## Version 3.9.0
 - Replace RankContent components

## Version 3.10.0
 - Add new content item space for heroes

## Version 3.11.0
 - Replace PremiumContent components

## Version 3.12.0
 - Automated update of generated Content Items classes

## Version 3.13.0
 - Replace ScenarioContent components

## Version 3.14.0
 - Replace ModContent components

## Version 4.0.0
 - Decouple ContentItemsCatalog from ContentItemsVersionedCatalogScriptable
 - Decouple ContentItemsVersioned from ContentItemsVersionedScriptable
 - Content Item scriptables are now only responsible for holding the serialized data.

## Version 4.1.0
 - Introduce ContentItemId property in IContent interface

## Version 4.2.0
 - Replace UnitContent components

## Version 4.3.0
 - Use NumericPiecewiseLinearFunction in ModMoraleBasedDmgFactorComponent directly

## Version 4.4.0
 - Replace ResearchContent components

## Version 4.5.0
 - Add missing deployment amount to the unit deploy config components

## Version 4.6.0
 - Replace `Cost` and `DailyCost` components

## Version 5.0.0
 - Remove `FeatureActivatableTag` as it has another definition elsewhere
 - Remove unwanted "if" statement inside `UnitClassComponent` initialization in `UnitContentDots`

## Version 5.0.1
 - Add `sealed` modifier to `RankContent`.

## Version 5.1.0
 - Expose army modifiers to ECS

## Version 6.0.0
 - Removes unused overloads for `ContentItemsBackendCollection.GetByVersionAsync`.
 - Adds `ContentItemsBackendCollection.GetContentItemsAsync` to allow retrieval of ContentItem instances.

## Version 6.0.1
 - Fix ContentItemsBackendCollection doesn't return the content if already cached
 -
## Version 6.1.0
 - Add UnitContent::GetNativeBaseTerrainType utility method

## Version 6.2.0
 - Add missing creation of `ModPlaneTrainIdComponent` and `ModAirConvoyTypeComponent` in `ModContentDots`.

## Version 6.3.0
 - Unused classes removed

## Version 7.0.0
 - Updates generated Content Items models.

## Version 8.0.0
 - Restore creating `ModAirplaneRefuelCostsComponent`.

## Version 8.1.0
 - Unused components removed.

## Version 9.0.0
 - Content Items JSONs are now serialized as binary files during edit mode, which are using during runtime instead of JSONs.

## Version 9.0.1
 - Fix issue with non-serialized properties as binaries in `MapFileReference`, `ContentItemId` and `ResourceId`.

## Version 10.0.0
 - InitializeDots on models now requires passing EntityManager instead of SystemState as parameter

## Version 10.0.1
 - Remove doubled `ModAirplaneRefuelCostsComponent`.
 - Add missing `ModFloorSpacePenaltyComponent`.

## Version 10.0.2
 - Fixes binary serialization/deserialization of `TimeSpan` and `ContentItemId`.

## Version 10.1.0
 - Moves Content Item components to their own files.

## Version 10.1.1
 - Regenerate `ContentItemsBackendCollectionScriptable` GUID.

## Version 10.2.0
 - Making use of `ContentItemsVersionedSharedCache` to prevent duplicate cache creation.

## Version 10.2.1
 - Fix converting terrain type names.

## Version 11.0.0
 - General cleanup.

## Version 11.1.0
 - Expose all Content Items type to `ContentItemDatabase`.

## Version 11.2.0
 - Replaced `PremiumPremiumTypeComponent` string value with the `PremiumType` enum.

## Version 11.3.0
 - Added `BaseTerrainTypeValueMap`.

## Version 11.4.0
 - Updating README.md

## Version 11.5.0
 - Replaced `UnitHitPoints.Value` type from `StringValueTuple[]` to `BaseTerrainTypeValueMap`.

## Version 11.6.0
 - Removed `ModProvinceMoraleComponent` and `ModProvinceMorale`.

## Version 11.7.0
 - Added `AwardContentDots`.

## Version 11.8.0
 - Removed `UnitViewWidthComponent` and `UnitViewWidthFactorsComponent` components.

## Version 11.9.0
 - Removed unused `ContentItemsIdSpace.Serialized`.

## Version 11.10.0
 - Added `ModContent` utilities.

## Version 11.11.0
 - Removed `UnitRanges` and `UnitRangesComponent` components.
 - Removed `UnitAirRanges` and `UnitAirRangesComponent` components

## Version 11.12.0
 - Split into 2 additional assemblies 'Generated' and 'Common'

## Version 11.13.0
 - Removed `Name` components initialization.
