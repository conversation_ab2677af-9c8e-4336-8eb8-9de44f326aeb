using Stratkit.Collections;
using Stratkit.ContentItemsModel.Generated;
using Stratkit.ContentItemsModel.Data;
using Stratkit.ServerCommunication;
using System.Collections.Generic;
using System.Linq;
using Unity.Collections;
using Unity.Entities;
using Unity.Mathematics;
using UnityEngine;
using AirplaneContent = Stratkit.ContentItemsModel.Data.AirplaneContent;
using DamageAuraContent = Stratkit.ContentItemsModel.Data.DamageAuraContent;
using Range = Stratkit.ContentItemsModel.Data.Range;
using UnitSpawnerContent = Stratkit.ContentItemsModel.Data.UnitSpawnerContent;

namespace Stratkit.ContentItemsModel.DOTS {
    public struct UnitContentDots : IContentDots<UnitContent> {
        public Entity Entity { get; set; }

        public UnitContentDots(Entity entity) {
            Entity = entity;
        }

        public void Initialize(
            EntityManager entityManager,
            IdMapper idMapper,
            BlobAssetStore blobAssetStore,
            UnitContent content
        ) {
            entityManager
                .AddBuffer<Range>(Entity)
                .Fill((k, v) => new Range(k, v), content.Ranges);

            entityManager
                .AddBuffer<AntiAirRange>(Entity)
                .Fill((k, v) => new AntiAirRange(k, v), content.AntiAirRanges);

            // Costs
            {
                Costs data = new();
                IdValueTuple[] dailyProductionsValues = content
                    .Costs
                    .Select(x => new IdValueTuple { Id = x.Key.Value, Amount = x.Value })
                    .ToArray();
                data.ValueEntities = EntityValueTupleUtils.ConvertIds(
                    idMapper,
                    dailyProductionsValues,
                    "ResourceType"
                );
                entityManager.AddComponentData(Entity, data);
            }

            // DailyCosts
            {
                DailyCosts data = new();
                IdValueTuple[] dailyCostsValues = content
                    .DailyCosts
                    .Select(x => new IdValueTuple { Id = x.Key.Value, Amount = x.Value })
                    .ToArray();
                data.ValueEntities = EntityValueTupleUtils.ConvertIds(
                    idMapper,
                    dailyCostsValues,
                    "ResourceType"
                );
                entityManager.AddComponentData(Entity, data);
            }

            // Speed
            {
                UnitSpeedComponent data = new() {
                    Speeds = content
                        .Speeds
                        .Select(x => new IdValueTuple { Id = (int)x.Key, Amount = x.Value })
                        .ToArray(),
                };
                entityManager.AddComponentData(Entity, data);

                using BlobHashMapBuilder<int, float> unitSpeedBuilder = new(Allocator.Temp);
                foreach (IdValueTuple tuple in data.Speeds) {
                    // speed is in units per minute so normalize to units per second
                    unitSpeedBuilder.Add(tuple.Id, tuple.Amount / 60f);
                }

                BlobAssetReference<BlobHashMap<int, float>> unitSpeed =
                    unitSpeedBuilder.CreateBlobAssetReference(Allocator.Persistent);
                blobAssetStore.TryAdd(ref unitSpeed);
                entityManager.AddComponentData(Entity, new UnitSpeed { Blob = unitSpeed });
            }

            // UnitManualConverterContent
            {
                if (content.ConvertManually != null) {
                    UnitManualConverterContent data = new() {
                        SpawnedUnitTypeId = content.ConvertManually.SpawnedUnitTypeId,
                        ConversionTime = (int)content.ConvertManually.ConversionTime.TotalSeconds,
                    };
                    entityManager.AddComponentData(Entity, data);

                    Entity spawnedUnitType = idMapper.GetEntity(("UnitType", data.SpawnedUnitTypeId));
                    entityManager.AddComponentData(
                        Entity,
                        new UnitManualConverter {
                            SpawnedUnitType = spawnedUnitType,
                            ConversionTime = data.ConversionTime,
                        }
                    );
                }
            }

            // AirplaneContent
            {
                if (content.Airplane != null) {
                    entityManager.AddComponentData(
                        Entity,
                        new AirplaneContent {
                            PatrolAllowed = content.Airplane.PatrolAllowed,
                            OperationalWithoutAirfield = content.Airplane.OperationalWithoutAirfield,
                        }
                    );
                }
            }

            // UnitSpawnerContent
            {
                if (content.SpawnUnit != null) {
                    UnitSpawnerContent data = new() {
                        SpawnedUnitTypeId = content.SpawnUnit.SpawnedUnitTypeId,
                        MobilizationTime = (int)content.SpawnUnit.MobilizationTime.TotalSeconds,
                        CanAttackOnSea = content.SpawnUnit.CanAttackOnSea,
                    };
                    entityManager.AddComponentData(Entity, data);

                    Entity spawnedUnitType = idMapper.GetEntity(("UnitType", data.SpawnedUnitTypeId));
                    entityManager.AddComponentData(
                        Entity,
                        new UnitSpawner {
                            SpawnedUnitType = spawnedUnitType,
                            MobilizationTime = data.MobilizationTime,
                            CanAttackOnSea = data.CanAttackOnSea,
                        }
                    );
                }
            }

            // UnitDeployContent
            {
                if (content.DeployUnit != null) {
                    UnitDeployUnitContent data = new() {
                        SpawnedUnitTypeId = content.DeployUnit.SpawnedUnitTypeId,
                        DeployAmount = content.DeployUnit.DeployAmount,
                        DeployTime = (int)content.DeployUnit.DeployTime.TotalSeconds,
                        DeployRange = content.DeployUnit.DeployRange,
                        DeployCost = content
                            .DeployUnit.DeployCost.Select(x => new IdValueTuple {
                                    Id = (int)x.Key,
                                    Amount = x.Value,
                                }
                            )
                            .ToArray(),
                    };
                    entityManager.AddComponentData(Entity, data);

                    Entity spawnedUnitType = idMapper.GetEntity(("UnitType", data.SpawnedUnitTypeId));
                    entityManager.AddComponentData(
                        Entity,
                        new UnitDeployUnit {
                            SpawnedUnitType = spawnedUnitType,
                            DeployAmount = data.DeployAmount,
                            DeployTime = data.DeployTime,
                            DeployRange = data.DeployRange,
                            MaxAmmunition = data.MaxAmmunition,
                            InitialAmmunition = data.InitialAmmunition,
                            ReloadTime = data.ReloadTime,
                        }
                    );

                    DynamicBuffer<UnitDeployCost> deployCosts = entityManager.AddBuffer<UnitDeployCost>(Entity);
                    foreach (IdValueTuple cost in data.DeployCost) {
                        Entity resource = idMapper.GetEntity(("ResourceType", cost.Id));
                        deployCosts.Add(
                            new UnitDeployCost {
                                Resource = resource,
                                Amount = cost.Amount,
                            }
                        );
                    }

                    entityManager.AddComponentData(Entity, data);
                }
            }

            // DamageAuraContent
            {
                if (content.DamageAura != null) {
                    entityManager.AddComponentData(
                        Entity,
                        new DamageAuraContent {
                            Radius = content.DamageAura.Radius,
                            DamageTickInterval = (int)content.DamageAura.DamageTickInterval.TotalSeconds,
                        }
                    );
                }
            }

            // BuildTime
            {
                entityManager.AddComponentData(
                    Entity,
                    new BuildTimeComponent {
                        BuildTime = content.BuildTimeSeconds(),
                    }
                );
            }

            // BuildingRequiredUpgrades
            {
                BuildingRequiredUpgrades data = new();
                IdValueTuple[] idValueTuples = content
                    .RequiredUpgrades
                    .Select(x => new IdValueTuple { Id = x.Key.Value, Amount = x.Value })
                    .ToArray();
                EntityValueTuple[] entityValueTuples =
                    EntityValueTupleUtils.ConvertIds(idMapper, idValueTuples, "UpgradeType");
                data.Value = entityValueTuples.Select(x => x.Entity).ToArray();
                entityManager.AddComponentData(Entity, data);
            }

            // UnitEmbarkmentComponent
            {
                if (content.Embarkment != null) {
                    entityManager.AddComponentData(
                        Entity,
                        new UnitEmbarkmentComponent {
                            EnemyEmbarkmentTime = (int)content.Embarkment.EnemyEmbarkmentTime.TotalSeconds,
                            FriendlyEmbarkmentTime = (int)content.Embarkment.FriendlyEmbarkmentTime.TotalSeconds,
                        }
                    );
                }
            }

            // UnitCustomSeaUnitTypeIdComponent
            {
                if (content.CustomSeaUnitTypeId != null) {
                    (FixedString64Bytes, long) valueTuple = ("UnitType", content.CustomSeaUnitTypeId.Value);
                    Entity referencedEntity = idMapper.GetEntity(valueTuple);
                    entityManager.AddComponentData(Entity, new UnitCustomSeaUnitType { Value = referencedEntity });
                }
            }

            // UnitExpirableSecondsComponent
            {
                if (content.Expirable != null) {
                    entityManager.AddComponentData(
                        Entity,
                        new UnitExpirableSecondsComponent {
                            Value = (int)content.Expirable.Value.TotalSeconds,
                        }
                    );
                }
            }

            // UnitFactionBaseItemId
            {
                if (content.FactionBaseItemID != null) {
                    UnitFactionBaseItemId data = new() {
                        Value = content.FactionBaseItemID.Value,
                    };
                    entityManager.AddComponentData(Entity, data);

                    (FixedString64Bytes, long) supId = ("UnitType", data.Value);
                    UnitFactionBaseItemComponent component = new() {
                        Value = idMapper.GetEntity(supId),
                    };
                    entityManager.AddComponent<UnitFactionBaseItemComponent>(Entity);
                    entityManager.SetComponentData(Entity, component);
                }
            }

            // RequiredResearchesComponent
            {
                RequiredResearchesComponent data = new() {
                    RequiredResearchesIds = content
                        .RequiredResearches
                        .Select(x => x.Value)
                        .ToArray(),
                };
                entityManager.AddComponentData(Entity, data);

                DynamicBuffer<RequiredResearches> requiredResearchesEnumerable =
                    entityManager.AddBuffer<RequiredResearches>(Entity);
                foreach (int dataRequiredResearchesId in data.RequiredResearchesIds) {
                    requiredResearchesEnumerable.Add(
                        new RequiredResearches {
                            ResearchId = dataRequiredResearchesId,
                            ResearchType = idMapper.GetEntity(("ResearchType", dataRequiredResearchesId)),
                        }
                    );
                }
            }

            // UnitMinProductionTimeComponent
            {
                if (content.MinProductionTime != null) {
                    entityManager.AddComponentData(
                        Entity,
                        new UnitMinProductionTimeComponent {
                            MinProductionTime = (int)content.MinProductionTime.Value.TotalSeconds,
                        }
                    );
                }
            }

            // UnitLevel
            {
                entityManager.AddComponentData(
                    Entity,
                    new UnitLevel {
                        Value = content.Tier,
                    }
                );
            }

            // UnitDamageTypeComponent
            {
                if (content.DamageTypes.Count > 0) {
                    UnitDamageTypeComponent data = new() {
                        DamageType = content
                            .DamageTypes
                            .Select(x => new StringValueTuple {
                                    Id = x.Key.ToString().ToUpper(),
                                    Amount = x.Value,
                                }
                            )
                            .Single(),
                    };
                    entityManager.AddComponentData(Entity, data);

                    long damageTypeId = (long)math.floor(data.DamageType.Amount);
                    (FixedString64Bytes, long) valueTuple = ("DamageType", damageTypeId);
                    Entity referencedEntity = idMapper.GetEntity(valueTuple);
                    UnitDamageType component = new() {
                        Value = referencedEntity,
                    };

                    entityManager.AddComponent<UnitDamageType>(Entity);
                    entityManager.SetComponentData(Entity, component);
                }
            }

            // UnitHitPoints
            {
                if (content.HitPoints.Count > 0) {
                    entityManager.AddComponentData(
                        Entity,
                        new UnitHitPoints {
                            Value = new BaseTerrainTypeValueMap(content.hitPoints),
                        }
                    );
                }
            }

            // UnitIngameName
            {
                entityManager.AddComponentData(
                    Entity,
                    new UnitIngameName {
                        Value = content.IngameName,
                    }
                );
            }

            // UnitDamageFactorsComponent
            {
                if (content.DamageFactors.Count > 0) {
                    UnitDamageFactorsComponent data = new() {
                        DamageFactorIds = content
                            .DamageFactors
                            .Select(x => new IdValueTuple { Id = (int)x.Key, Amount = x.Value })
                            .ToArray(),
                    };
                    data.DamageFactorEntities = data
                        .DamageFactorIds
                        .Select(x => new EntityValueTuple {
                                Entity = idMapper.GetEntity(("DamageType", x.Id)),
                                Amount = x.Amount,
                            }
                        )
                        .ToArray();
                    entityManager.AddComponentData(Entity, data);
                }
            }

            // UnitDefenceFactorsComponent
            {
                if (content.DefenceFactors.Count > 0) {
                    entityManager.AddComponentData(
                        Entity,
                        new UnitDefenceFactorsComponent {
                            DefenceFactors = content
                                .DefenceFactors
                                .Select(x => new StringValueTuple { Id = x.Key.ToString().ToUpper(), Amount = x.Value })
                                .ToArray(),
                        }
                    );
                }
            }

            // UnitDefenceComponent
            {
                if (content.Defence.Count > 0) {
                    entityManager.AddComponentData(
                        Entity,
                        new UnitDefenceComponent {
                            Defence = content
                                .Defence
                                .Select(x => new StringValueTuple { Id = x.Key.ToString().ToUpper(), Amount = x.Value })
                                .ToArray(),
                        }
                    );
                }
            }

            // UnitStrengthComponent
            {
                if (content.Strength.Count > 0) {
                    entityManager.AddComponentData(
                        Entity,
                        new UnitStrengthComponent {
                            Strength = content
                                .Strength
                                .Select(x => new StringValueTuple { Id = x.Key.ToString().ToUpper(), Amount = x.Value })
                                .ToArray(),
                        }
                    );
                }
            }

            // UnitSpeedFactorsComponent
            {
                UnitSpeedFactorsComponent data = new() {
                    SpeedFactors = content
                        .SpeedFactors
                        .Select(x => new IdValueTuple { Id = (int)x.Key, Amount = x.Value })
                        .ToArray(),
                    FriendlySpeedFactor = content.FriendlySpeedFactor,
                    ForeignSpeedFactor = content.ForeignSpeedFactor,
                };
                entityManager.AddComponentData(Entity, data);

                using BlobHashMapBuilder<int, float> unitSpeedFactorsBuilder = new(Allocator.Temp);
                foreach (IdValueTuple tuple in data.SpeedFactors) {
                    unitSpeedFactorsBuilder.Add(tuple.Id, tuple.Amount);
                }

                BlobAssetReference<BlobHashMap<int, float>> unitSpeedFactors =
                    unitSpeedFactorsBuilder.CreateBlobAssetReference(Allocator.Persistent);
                blobAssetStore.TryAdd(ref unitSpeedFactors);

                entityManager.AddComponentData(
                    Entity,
                    new UnitSpeedFactors {
                        TerrainBlob = unitSpeedFactors,
                        Friendly = (float)data.FriendlySpeedFactor,
                        Foreign = (float)data.ForeignSpeedFactor,
                    }
                );
            }

            // UnitStrengthFactorsComponent
            {
                if (content.StrengthFactors.Count > 0) {
                    entityManager.AddComponentData(
                        Entity,
                        new UnitStrengthFactorsComponent {
                            StrengthFactors = content
                                .StrengthFactors
                                .Select(x => new StringValueTuple { Id = x.Key.ToString().ToUpper(), Amount = x.Value })
                                .ToArray(),
                        }
                    );
                }
            }

            // UnitStatsColumnId
            {
                entityManager.AddComponentData(
                    Entity,
                    new UnitStatsColumnId {
                        Value = content.StatsColumnID,
                    }
                );
            }

            // UnitClassComponent
            {
                UnitClassComponent data = new() {
                    Value = content.UnitClass,
                };
                entityManager.AddComponentData(Entity, data);
                (FixedString64Bytes, long) valueTuple = ("UnitClass", data.Value);
                Entity referencedEntity = idMapper.GetEntity(valueTuple);
                entityManager.AddComponentData(
                    Entity,
                    new UnitClass {
                        Value = referencedEntity,
                    }
                );
            }

            // UnitViewWidthsComponent
            {
                if (content.ViewWidths.Count > 0) {
                    using BlobHashMapBuilder<int, float> viewWidthsBuilder = new(Allocator.Temp);
                    foreach ((BaseTerrainType terrain, float value) in content.ViewWidths) {
                        viewWidthsBuilder.Add((int)terrain, value);
                    }

                    BlobAssetReference<BlobHashMap<int, float>> viewWidths =
                        viewWidthsBuilder.CreateBlobAssetReference(Allocator.Persistent);
                    blobAssetStore.TryAdd(ref viewWidths);
                    entityManager.AddComponentData(Entity, new UnitViewWidths { Blob = viewWidths });
                }
            }

            // BelongsToFactionsIdComponent
            {
                BelongsToFactionsIdComponent data = new() {
                    Value = content.Factions.Select(x => (int)x).ToArray(),
                };
                entityManager.AddComponentData(Entity, data);
                DynamicBuffer<BelongsToFactionComponent> belongsToFactionComponents =
                    entityManager.AddBuffer<BelongsToFactionComponent>(Entity);
                foreach (int factionId in data.Value) {
                    if (idMapper.TryGetEntity(("FactionsType", factionId), out Entity factionEntity)) {
                        belongsToFactionComponents.Add(
                            new BelongsToFactionComponent {
                                Value = factionEntity,
                            }
                        );
                    }
                }
            }

            // UnitProductionSpeedupCostFactor
            {
                if (content.UnitFeatures.TryGetValue(UnitFeature.ProductionSpeedupCostFactor, out float value)) {
                    entityManager.AddComponentData(
                        Entity,
                        new UnitProductionSpeedupCostFactor {
                            Value = value,
                        }
                    );
                }
            }

            // UnitTypeTag
            {
                entityManager.AddComponent<UnitTypeTag>(Entity);
            }

            // UnitMoraleBasedDamageFactorComponent
            {
                if (content.MoraleBasedDmgFactor.Count > 0) {
                    entityManager.AddComponentData(
                        Entity,
                        new UnitMoraleBasedDamageFactorComponent {
                            Value = content
                                .MoraleBasedDmgFactor
                                .Select(x => new ValueValueTuple {
                                        Key = x.Key,
                                        Value = x.Value,
                                    }
                                )
                                .ToArray(),
                        }
                    );
                }
            }

            // UnitHitpointSizeFactorsComponent
            {
                if (content.HitpointSizeFactors.Count > 0) {
                    UnitHitpointSizeFactorsComponent data = new() {
                        Value = content
                            .HitpointSizeFactors
                            .Select(x => new IdValueTuple {
                                    Id = (int)x.Key,
                                    Amount = x.Value,
                                }
                            )
                            .ToArray(),
                    };
                    entityManager.AddComponentData(Entity, data);

                    DynamicBuffer<UnitHitpointSizeFactors> unitSizeFactorsEnumerable =
                        entityManager.AddBuffer<UnitHitpointSizeFactors>(Entity);
                    foreach (IdValueTuple tuple in data.Value) {
                        unitSizeFactorsEnumerable.Add(
                            new UnitHitpointSizeFactors { Size = tuple.Id, Value = tuple.Amount }
                        );
                    }
                }
            }

            // UnitStrengthSizeFactorsComponent
            {
                if (content.StrengthSizeFactors.Count > 0) {
                    UnitStrengthSizeFactorsComponent data = new() {
                        Value = content
                            .StrengthSizeFactors
                            .Select(x => new IdValueTuple {
                                    Id = (int)x.Key,
                                    Amount = x.Value,
                                }
                            )
                            .ToArray(),
                    };
                    entityManager.AddComponentData(Entity, data);

                    DynamicBuffer<UnitStrengthSizeFactors> unitSizeFactorsEnumerable =
                        entityManager.AddBuffer<UnitStrengthSizeFactors>(Entity);
                    foreach (IdValueTuple tuple in data.Value) {
                        unitSizeFactorsEnumerable.Add(
                            new UnitStrengthSizeFactors { Size = tuple.Id, Value = tuple.Amount }
                        );
                    }
                }
            }

            // UnitAdditionalProductionComponent
            {
                if (content.AdditionalProduction != null) {
                    static UnitAdditionalProduction CreateUnitAdditionalProduction(
                        IdMapper idMapper,
                        UnitAdditionalProductionComponent data
                    ) {
                        // Ensure build time factor starts with (1,1)
                        List<ValueValueTuple> buildTimeList = data.BuildTimeFactor.ToList();
                        if (buildTimeList.Count == 0 || !Mathf.Approximately(buildTimeList[0].Key, 1f)) {
                            buildTimeList.Insert(0, new ValueValueTuple { Key = 1f, Value = 1f });
                        }

                        // Ensure cost factor starts with (1,1)
                        List<ValueValueTuple> costFactorList = data.CostFactor.ToList();
                        if (costFactorList.Count == 0 || !Mathf.Approximately(costFactorList[0].Key, 1f)) {
                            costFactorList.Insert(0, new ValueValueTuple { Key = 1f, Value = 1f });
                        }

                        NativeHashMap<Entity, int> maxAmount = new(data.MaxAmount.Length, Allocator.Persistent);
                        for (int i = 0, imax = data.MaxAmount.Length; i < imax; i++) {
                            int id = data.MaxAmount[i].Id;
                            float amount = data.MaxAmount[i].Amount;
                            (FixedString64Bytes, long) supId = ("UpgradeType", id);
                            Entity entity = idMapper.GetEntity(supId);
                            maxAmount[entity] = (int)amount;
                        }

                        return new UnitAdditionalProduction {
                            MaxAmount = maxAmount,
                            BuildTimeFactor = new NativeNumericPiecewiseLinearFunction(
                                buildTimeList.ToArray(),
                                Allocator.Persistent
                            ),
                            CostFactor = new NativeNumericPiecewiseLinearFunction(
                                costFactorList.ToArray(),
                                Allocator.Persistent
                            ),
                        };
                    }

                    UnitAdditionalProductionComponent data = new() {
                        MaxAmount = content
                            .AdditionalProduction.MaxAmount
                            .Select(x => new IdValueTuple { Id = x.Key, Amount = x.Value })
                            .ToArray(),
                        BuildTimeFactor = content
                            .AdditionalProduction.BuildTimeFactor.Select(x => new ValueValueTuple {
                                    Key = x.Key,
                                    Value = x.Value,
                                }
                            )
                            .ToArray(),
                        CostFactor = content
                            .AdditionalProduction.CostFactor.Select(x => new ValueValueTuple {
                                    Key = x.Key,
                                    Value = x.Value,
                                }
                            )
                            .ToArray(),
                    };
                    ;
                    entityManager.AddComponentData(Entity, data);
                    entityManager.AddComponentData(Entity, CreateUnitAdditionalProduction(idMapper, data));
                }
            }

            // UnitSet
            entityManager.AddComponentData(Entity, new UnitSet { Value = content.Set });

            // UnitIdentifierComponent
            entityManager.AddComponentData(Entity, new UnitIdentifierComponent { Value = content.Identifier });

            // Extras
            {
                UnitContentItemExtras data = new() {
                    Identifier = content.Identifier,
                    Conquerer = content.Conquerer,
                    Garrison = content.Garrison,
                    AttackAllowed = content.AttackAllowed,
                    DamageArea = content
                        .DamageArea.Select(x => new StringValueTuple {
                                Id = x.Key.ToString().ToUpper(),
                                Amount = x.Value,
                            }
                        )
                        .ToArray(),
                    UnitFeatures = content
                        .UnitFeatures.Select(x => new StringValueTuple {
                                Id = x.Key.ToString(),
                                Amount = x.Value,
                            }
                        )
                        .ToArray(),
                };

                using BlobHashMapBuilder<int, float> featuresBuilder = new(Allocator.Temp);
                foreach ((UnitFeature id, float value) in content.UnitFeatures) {
                    featuresBuilder.Add((int)id, value);
                }

                BlobAssetReference<BlobHashMap<int, float>> features =
                    featuresBuilder.CreateBlobAssetReference(Allocator.Persistent);
                blobAssetStore.TryAdd(ref features);
                entityManager.AddComponentData(Entity, new UnitFeatures { Blob = features });

                UnitDamageArea damageArea = default;
                foreach (StringValueTuple tuple in data.DamageArea) {
                    damageArea.Value.Add((tuple.Id, tuple.Amount));
                }

                entityManager.AddComponentData(Entity, damageArea);

                entityManager.AddComponentData(
                    Entity,
                    new UnitAllowedToAttack {
                        Value = data.AttackAllowed,
                    }
                );
            }

            // army modifiers
            {
                if (content.ArmyModifiersConfig != null) {
                    DynamicBuffer<UnitArmyModifierConfig> buffer =
                        entityManager.AddBuffer<UnitArmyModifierConfig>(Entity);
                    foreach (ArmyModifierSet set in content.ArmyModifiersConfig.ArmyModifierSets.Values) {
                        foreach (ArmyModifierContent modifier in set.ArmyModifiers.Values) {
                            Entity affectedUnitTypesEntity =
                                entityManager.CreateEntity(
                                    stackalloc ComponentType[] {
                                        ComponentType.ReadWrite<UnitArmyModifierAffectedUnitTypeId>(),
                                    }
                                );
                            DynamicBuffer<UnitArmyModifierAffectedUnitTypeId> affectedUnitTypesBuffer =
                                entityManager.GetBuffer<UnitArmyModifierAffectedUnitTypeId>(affectedUnitTypesEntity);
                            foreach (ContentItemId id in modifier.AffectedUnitTypes) {
                                affectedUnitTypesBuffer.Add(new UnitArmyModifierAffectedUnitTypeId { Value = id });
                            }

                            Entity enablingTerritoryTypesEntity =
                                entityManager.CreateEntity(
                                    stackalloc ComponentType[] {
                                        ComponentType.ReadWrite<UnitArmyModifierEnablingTerritoryType>()
                                    }
                                );
                            DynamicBuffer<UnitArmyModifierEnablingTerritoryType> enablingTerritoryTypes =
                                entityManager.GetBuffer<UnitArmyModifierEnablingTerritoryType>(
                                    enablingTerritoryTypesEntity
                                );
                            foreach (TerritoryType type in modifier.EnablingTerritoryTypes) {
                                enablingTerritoryTypes.Add(new UnitArmyModifierEnablingTerritoryType { Value = type });
                            }

                            buffer.Add(
                                new UnitArmyModifierConfig {
                                    RequiredInventoryItemId = set.RequiredInventoryItem
                                        ?? new ContentItemId(ContentItemsIdSpace.BackEnd, -1),
                                    AffectedUnitTypesEntity = affectedUnitTypesEntity,
                                    EnablingTerritoryTypesEntity = enablingTerritoryTypesEntity,
                                    ArmyVisionFactorModifier = modifier.ArmyVisionFactorModifier,
                                    ArmyVisionFlatModifier = modifier.ArmyVisionFlatModifier,
                                    AttackFactorModifier = modifier.AttackFactorModifier,
                                    DefenceFactorModifier = modifier.DefenceFactorModifier,
                                    HitPointsFactorModifier = modifier.HitpointsFactorModifier,
                                    SpeedFactorModifier = modifier.SpeedFactorModifier,
                                    SpeedFlatModifier = modifier.SpeedFlatModifier,
                                }
                            );
                        }
                    }
                }
            }
        }

        public DynamicBuffer<Range> GetRanges(ref SystemState state) {
            DynamicBuffer<Range> buffer = state.EntityManager.GetBuffer<Range>(Entity);
            return buffer;
        }

        public float GetRange(ref SystemState state, BaseTerrainType key) {
            DynamicBuffer<Range> buffer = state.EntityManager.GetBuffer<Range>(Entity);
            return buffer.FindEnumKey<Range, BaseTerrainType, float>(key);
        }

        public DynamicBuffer<AntiAirRange> GetAntiAirRanges(ref SystemState state) {
            DynamicBuffer<AntiAirRange> buffer = state.EntityManager.GetBuffer<AntiAirRange>(Entity);
            return buffer;
        }

        public float GetAntiAirRange(ref SystemState state, BaseTerrainType key) {
            DynamicBuffer<AntiAirRange> buffer =
                state.EntityManager.GetBuffer<AntiAirRange>(Entity);
            return buffer.FindEnumKey<AntiAirRange, BaseTerrainType, float>(key);
        }
    }
}
